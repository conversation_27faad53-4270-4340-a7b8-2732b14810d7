import type { MoodOption } from '../types';

export const MOOD_OPTIONS: MoodOption[] = [
  { value: 1, emoji: '😢', label: 'Very Sad' },
  { value: 2, emoji: '😔', label: 'Sad' },
  { value: 3, emoji: '😐', label: 'Neutral' },
  { value: 4, emoji: '😊', label: 'Happy' },
  { value: 5, emoji: '😄', label: 'Very Happy' },
];

export const getMoodEmoji = (mood: number): string => {
  const moodOption = MOOD_OPTIONS.find(option => option.value === mood);
  return moodOption?.emoji || '😐';
};

export const getMoodLabel = (mood: number): string => {
  const moodOption = MOOD_OPTIONS.find(option => option.value === mood);
  return moodOption?.label || 'Neutral';
};

export const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
];

export const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'high':
      return 'text-error';
    case 'medium':
      return 'text-warning';
    case 'low':
      return 'text-success';
    default:
      return 'text-neutral-500';
  }
};
