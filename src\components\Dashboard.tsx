import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, CheckSquare, BookOpen, Calendar as CalendarIcon } from 'lucide-react';
import { Card, Button } from './ui';
import { getTodos, getJournalEntry } from '../utils/storage';
import { getGreeting, getTodayString, formatDisplayDate } from '../utils/dateHelpers';
import { getMoodEmoji } from '../constants/moods';
import { DashboardStats } from '../types';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    todaysTodos: 0,
    incompleteTodos: 0,
    hasJournalEntry: false,
  });

  useEffect(() => {
    const loadStats = () => {
      const todos = getTodos();
      const today = getTodayString();
      const todayEntry = getJournalEntry(today);

      const todaysTodos = todos.filter(todo => 
        todo.dueDate === today && !todo.completed
      ).length;

      const incompleteTodos = todos.filter(todo => !todo.completed).length;

      setStats({
        todaysTodos,
        incompleteTodos,
        hasJournalEntry: !!todayEntry,
        currentMood: todayEntry?.mood,
      });
    };

    loadStats();
    
    // Refresh stats when the component becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadStats();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  const today = new Date();
  const greeting = getGreeting();

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Welcome Section */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-neutral-900">
          {greeting}! 👋
        </h1>
        <p className="text-lg text-neutral-600">
          {formatDisplayDate(today)}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Today's To-Dos */}
        <Card className="text-center">
          <div className="space-y-3">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <CheckSquare className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-neutral-900">
                {stats.todaysTodos}
              </h3>
              <p className="text-neutral-600">
                {stats.todaysTodos === 1 ? 'Task due today' : 'Tasks due today'}
              </p>
            </div>
          </div>
        </Card>

        {/* Total Incomplete To-Dos */}
        <Card className="text-center">
          <div className="space-y-3">
            <div className="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mx-auto">
              <CheckSquare className="w-6 h-6 text-secondary" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-neutral-900">
                {stats.incompleteTodos}
              </h3>
              <p className="text-neutral-600">
                {stats.incompleteTodos === 1 ? 'Task remaining' : 'Tasks remaining'}
              </p>
            </div>
          </div>
        </Card>

        {/* Today's Journal */}
        <Card className="text-center">
          <div className="space-y-3">
            <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center mx-auto">
              <BookOpen className="w-6 h-6 text-accent" />
            </div>
            <div>
              <div className="text-2xl font-bold text-neutral-900">
                {stats.hasJournalEntry ? (
                  <span className="flex items-center justify-center space-x-2">
                    <span>✓</span>
                    {stats.currentMood && (
                      <span>{getMoodEmoji(stats.currentMood)}</span>
                    )}
                  </span>
                ) : (
                  '—'
                )}
              </div>
              <p className="text-neutral-600">
                {stats.hasJournalEntry ? 'Journal written' : 'No journal entry'}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-neutral-900">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link to="/todos">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <Plus className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-900">Add Today's To-Dos</h3>
                  <p className="text-sm text-neutral-600">
                    Manage your tasks and stay organized
                  </p>
                </div>
              </div>
            </Card>
          </Link>

          <Link to={`/journal/${getTodayString()}`}>
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center">
                  <BookOpen className="w-6 h-6 text-accent" />
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-900">Write Journal Entry</h3>
                  <p className="text-sm text-neutral-600">
                    Reflect on your day and track your mood
                  </p>
                </div>
              </div>
            </Card>
          </Link>
        </div>
      </div>

      {/* Calendar Preview */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-neutral-900">Calendar</h2>
          <Link to="/calendar">
            <Button variant="outline" size="sm">
              <CalendarIcon className="w-4 h-4 mr-2" />
              View Full Calendar
            </Button>
          </Link>
        </div>
        <Card>
          <div className="text-center py-8">
            <CalendarIcon className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
            <p className="text-neutral-600">
              View your journal entries and mood tracking in the calendar view
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
