import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Card, Button } from './ui';
import { JournalEntry } from '../types';
import { getJournalEntries } from '../utils/storage';
import { getCalendarDays, formatDateForInput, getTodayString } from '../utils/dateHelpers';
import { getMoodEmoji } from '../constants/moods';

const Calendar: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);

  useEffect(() => {
    loadJournalEntries();
  }, []);

  const loadJournalEntries = () => {
    const entries = getJournalEntries();
    setJournalEntries(entries);
  };

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const calendarDays = getCalendarDays(year, month);

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const goToPreviousMonth = () => {
    setCurrentDate(new Date(year, month - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(year, month + 1, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const getJournalEntryForDate = (date: Date): JournalEntry | undefined => {
    const dateString = formatDateForInput(date);
    return journalEntries.find(entry => entry.date === dateString);
  };

  const isToday = (date: Date): boolean => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date): boolean => {
    return date.getMonth() === month;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">Calendar</h1>
        <Button onClick={goToToday} variant="outline" className="w-full sm:w-auto">
          Today
        </Button>
      </div>

      {/* Calendar Navigation */}
      <Card>
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={goToPreviousMonth}
            className="p-2 rounded-lg hover:bg-neutral-100 transition-colors"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>

          <h2 className="text-xl font-semibold text-neutral-900">
            {monthNames[month]} {year}
          </h2>

          <button
            onClick={goToNextMonth}
            className="p-2 rounded-lg hover:bg-neutral-100 transition-colors"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day Headers */}
          {dayNames.map((day) => (
            <div
              key={day}
              className="p-2 sm:p-3 text-center text-xs sm:text-sm font-medium text-neutral-600 border-b border-neutral-200"
            >
              {day}
            </div>
          ))}

          {/* Calendar Days */}
          {calendarDays.map((date, index) => {
            const entry = getJournalEntryForDate(date);
            const isCurrentDay = isToday(date);
            const isInCurrentMonth = isCurrentMonth(date);
            const dateString = formatDateForInput(date);

            return (
              <Link
                key={index}
                to={`/journal/${dateString}`}
                className={`relative p-2 sm:p-3 min-h-12 sm:min-h-16 border border-neutral-100 hover:bg-neutral-50 transition-all duration-200 ${
                  !isInCurrentMonth ? 'text-neutral-400 bg-neutral-25' : ''
                } ${isCurrentDay ? 'bg-primary/10 border-primary' : ''}`}
              >
                <div className="flex flex-col items-center space-y-1">
                  <span className={`text-sm ${
                    isCurrentDay ? 'font-bold text-primary' :
                    isInCurrentMonth ? 'text-neutral-900' : 'text-neutral-400'
                  }`}>
                    {date.getDate()}
                  </span>

                  {/* Mood Indicator */}
                  {entry?.mood && (
                    <span className="text-lg">
                      {getMoodEmoji(entry.mood)}
                    </span>
                  )}

                  {/* Journal Entry Indicator */}
                  {entry && !entry.mood && (
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  )}
                </div>
              </Link>
            );
          })}
        </div>
      </Card>

      {/* Legend */}
      <Card>
        <h3 className="text-lg font-semibold text-neutral-900 mb-4">Legend</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 bg-primary rounded-full"></div>
            <span className="text-sm text-neutral-600">Has journal entry</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-lg">😊</span>
            <span className="text-sm text-neutral-600">Mood tracked</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 border-2 border-primary bg-primary/10 rounded"></div>
            <span className="text-sm text-neutral-600">Today</span>
          </div>
          <div className="flex items-center space-x-3">
            <Plus className="w-4 h-4 text-neutral-400" />
            <span className="text-sm text-neutral-600">Click any day to add entry</span>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Calendar;
