<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Journal App Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Daily Journal App - Functionality Test</h1>
    
    <div class="info test-result">
        <strong>Testing Instructions:</strong>
        <ol>
            <li>The app should load below without any errors</li>
            <li>You should see a clean, minimalist dashboard with today's greeting</li>
            <li>Navigation should work between Home, To-Dos, and Calendar</li>
            <li>Try clicking "Load Demo Data" to populate the app with sample data</li>
            <li>Test adding a new todo item</li>
            <li>Test creating a journal entry</li>
        </ol>
    </div>

    <div class="test-result">
        <strong>App Status:</strong> 
        <span id="status">Loading...</span>
    </div>

    <div>
        <button onclick="testConnection()">Test Server Connection</button>
        <button onclick="reloadApp()">Reload App</button>
        <button onclick="openInNewTab()">Open in New Tab</button>
    </div>

    <iframe id="appFrame" src="http://localhost:5173/" onload="checkAppLoad()" onerror="showError()"></iframe>

    <div id="testResults"></div>

    <script>
        function testConnection() {
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        showResult('Server connection successful! Status: ' + response.status, 'success');
                    } else {
                        showResult('Server responded with status: ' + response.status, 'error');
                    }
                })
                .catch(error => {
                    showResult('Connection failed: ' + error.message, 'error');
                });
        }

        function checkAppLoad() {
            showResult('App iframe loaded successfully!', 'success');
            document.getElementById('status').textContent = 'Running ✅';
            document.getElementById('status').style.color = 'green';
        }

        function showError() {
            showResult('Failed to load the app in iframe', 'error');
            document.getElementById('status').textContent = 'Error ❌';
            document.getElementById('status').style.color = 'red';
        }

        function reloadApp() {
            document.getElementById('appFrame').src = 'http://localhost:5173/';
        }

        function openInNewTab() {
            window.open('http://localhost:5173/', '_blank');
        }

        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result ' + type;
            resultDiv.textContent = new Date().toLocaleTimeString() + ': ' + message;
            resultsDiv.appendChild(resultDiv);
        }

        // Test connection on page load
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
