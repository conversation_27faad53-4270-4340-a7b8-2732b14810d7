import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, Camera, MapPin, Calendar as CalendarIcon } from 'lucide-react';
import { Card, Button, Input } from './ui';
import type { JournalEntry as JournalEntryType, MoodValue } from '../types';
import { getJournalEntry, saveJournalEntry, generateId } from '../utils/storage';
import { formatDisplayDate } from '../utils/dateHelpers';
import { MOOD_OPTIONS } from '../constants/moods';

const JournalEntry: React.FC = () => {
  const { date } = useParams<{ date: string }>();
  const navigate = useNavigate();

  const [entry, setEntry] = useState<JournalEntryType>({
    id: '',
    date: date || '',
    content: '',
    createdAt: '',
    updatedAt: '',
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (!date) {
      navigate('/calendar');
      return;
    }

    loadEntry();
  }, [date, navigate]);

  const loadEntry = () => {
    setIsLoading(true);
    const existingEntry = getJournalEntry(date!);

    if (existingEntry) {
      setEntry(existingEntry);
    } else {
      // Create new entry structure
      setEntry({
        id: generateId(),
        date: date!,
        content: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    setIsLoading(false);
    setHasChanges(false);
  };

  const handleSave = async () => {
    if (!entry.content.trim() && !entry.mood && !entry.location) {
      return; // Don't save empty entries
    }

    setIsSaving(true);

    try {
      const entryToSave: JournalEntryType = {
        ...entry,
        updatedAt: new Date().toISOString(),
      };

      saveJournalEntry(entryToSave);
      setHasChanges(false);

      // Show success feedback
      setTimeout(() => {
        setIsSaving(false);
      }, 500);
    } catch (error) {
      console.error('Error saving journal entry:', error);
      setIsSaving(false);
    }
  };

  const handleContentChange = (content: string) => {
    setEntry(prev => ({ ...prev, content }));
    setHasChanges(true);
  };

  const handleMoodChange = (mood: MoodValue) => {
    setEntry(prev => ({ ...prev, mood }));
    setHasChanges(true);
  };

  const handleLocationChange = (location: string) => {
    setEntry(prev => ({ ...prev, location }));
    setHasChanges(true);
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    // For now, we'll just show an alert since we're using localStorage
    // In a real app, you'd upload to a server or convert to base64
    alert('Photo upload functionality would be implemented here. For this demo, photos are stored as placeholders.');

    const photoPlaceholders = Array.from(files).map((_, index) =>
      `photo-${Date.now()}-${index}.jpg`
    );

    setEntry(prev => ({
      ...prev,
      photos: [...(prev.photos || []), ...photoPlaceholders]
    }));
    setHasChanges(true);
  };

  const removePhoto = (index: number) => {
    setEntry(prev => ({
      ...prev,
      photos: prev.photos?.filter((_, i) => i !== index)
    }));
    setHasChanges(true);
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-neutral-200 rounded w-1/3 mb-6"></div>
          <Card>
            <div className="space-y-4">
              <div className="h-4 bg-neutral-200 rounded w-1/4"></div>
              <div className="h-32 bg-neutral-200 rounded"></div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  const displayDate = date ? new Date(date) : new Date();

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/calendar')}
            className="p-2 rounded-lg hover:bg-neutral-100 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">Journal Entry</h1>
            <p className="text-neutral-600 flex items-center space-x-2">
              <CalendarIcon className="w-4 h-4" />
              <span>{formatDisplayDate(displayDate)}</span>
            </p>
          </div>
        </div>

        <Button
          onClick={handleSave}
          disabled={!hasChanges || isSaving}
          className="flex items-center justify-center space-x-2 w-full sm:w-auto"
        >
          <Save className="w-4 h-4" />
          <span>{isSaving ? 'Saving...' : 'Save'}</span>
        </Button>
      </div>

      {/* Mood Selector */}
      <Card>
        <h3 className="text-lg font-semibold text-neutral-900 mb-4">How are you feeling today?</h3>
        <div className="grid grid-cols-5 gap-2 sm:flex sm:flex-wrap sm:gap-3">
          {MOOD_OPTIONS.map((mood) => (
            <button
              key={mood.value}
              onClick={() => handleMoodChange(mood.value)}
              className={`flex flex-col items-center p-2 sm:p-3 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${
                entry.mood === mood.value
                  ? 'border-primary bg-primary/10'
                  : 'border-neutral-200 hover:border-neutral-300'
              }`}
            >
              <span className="text-xl sm:text-2xl mb-1">{mood.emoji}</span>
              <span className="text-xs text-neutral-600 hidden sm:block">{mood.label}</span>
            </button>
          ))}
        </div>
      </Card>

      {/* Journal Content */}
      <Card>
        <h3 className="text-lg font-semibold text-neutral-900 mb-4">What's on your mind?</h3>
        <textarea
          value={entry.content}
          onChange={(e) => handleContentChange(e.target.value)}
          placeholder="Write about your day, thoughts, experiences, or anything you'd like to remember..."
          className="w-full h-64 p-4 border border-neutral-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
          style={{ fontFamily: 'inherit' }}
        />
        <div className="mt-2 text-right">
          <span className="text-sm text-neutral-500">
            {entry.content.length} characters
          </span>
        </div>
      </Card>

      {/* Photos Section */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-neutral-900">Photos</h3>
          <label className="cursor-pointer">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handlePhotoUpload}
              className="hidden"
            />
            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <Camera className="w-4 h-4" />
              <span>Add Photos</span>
            </Button>
          </label>
        </div>

        {entry.photos && entry.photos.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {entry.photos.map((photo, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square bg-neutral-100 rounded-lg flex items-center justify-center border-2 border-dashed border-neutral-300">
                  <div className="text-center">
                    <Camera className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
                    <p className="text-xs text-neutral-500 break-all">{photo}</p>
                  </div>
                </div>
                <button
                  onClick={() => removePhoto(index)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-error text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 border-2 border-dashed border-neutral-300 rounded-lg">
            <Camera className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
            <p className="text-neutral-600">No photos added yet</p>
            <p className="text-sm text-neutral-500 mt-1">
              Click "Add Photos" to attach images to this entry
            </p>
          </div>
        )}
      </Card>

      {/* Location */}
      <Card>
        <h3 className="text-lg font-semibold text-neutral-900 mb-4">Location</h3>
        <div className="flex items-center space-x-3">
          <MapPin className="w-5 h-5 text-neutral-400" />
          <Input
            value={entry.location || ''}
            onChange={(e) => handleLocationChange(e.target.value)}
            placeholder="Where are you? (optional)"
            className="flex-1"
          />
        </div>
      </Card>

      {/* Save Reminder */}
      {hasChanges && (
        <div className="fixed bottom-4 right-4 md:bottom-6 md:right-6">
          <Card className="bg-accent text-white shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">You have unsaved changes</span>
              <Button
                onClick={handleSave}
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20"
              >
                Save Now
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default JournalEntry;
