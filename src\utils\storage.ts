import { AppData, Todo, JournalEntry } from '../types';

const STORAGE_KEY = 'daily-journal-app-data';

// Default data structure
const defaultData: AppData = {
  todos: [],
  journalEntries: [],
};

// Get all data from localStorage
export const getData = (): AppData => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return defaultData;
    
    const parsed = JSON.parse(stored);
    return {
      ...defaultData,
      ...parsed,
    };
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return defaultData;
  }
};

// Save all data to localStorage
export const saveData = (data: AppData): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

// Todo operations
export const getTodos = (): Todo[] => {
  return getData().todos;
};

export const saveTodo = (todo: Todo): void => {
  const data = getData();
  const existingIndex = data.todos.findIndex(t => t.id === todo.id);
  
  if (existingIndex >= 0) {
    data.todos[existingIndex] = todo;
  } else {
    data.todos.push(todo);
  }
  
  saveData(data);
};

export const deleteTodo = (todoId: string): void => {
  const data = getData();
  data.todos = data.todos.filter(t => t.id !== todoId);
  saveData(data);
};

export const toggleTodo = (todoId: string): void => {
  const data = getData();
  const todo = data.todos.find(t => t.id === todoId);
  
  if (todo) {
    todo.completed = !todo.completed;
    todo.completedAt = todo.completed ? new Date().toISOString() : undefined;
    saveData(data);
  }
};

// Journal operations
export const getJournalEntries = (): JournalEntry[] => {
  return getData().journalEntries;
};

export const getJournalEntry = (date: string): JournalEntry | undefined => {
  const entries = getJournalEntries();
  return entries.find(entry => entry.date === date);
};

export const saveJournalEntry = (entry: JournalEntry): void => {
  const data = getData();
  const existingIndex = data.journalEntries.findIndex(e => e.date === entry.date);
  
  if (existingIndex >= 0) {
    data.journalEntries[existingIndex] = {
      ...entry,
      updatedAt: new Date().toISOString(),
    };
  } else {
    data.journalEntries.push(entry);
  }
  
  saveData(data);
};

export const deleteJournalEntry = (date: string): void => {
  const data = getData();
  data.journalEntries = data.journalEntries.filter(e => e.date !== date);
  saveData(data);
};

// Utility functions
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0]; // YYYY-MM-DD
};

export const isToday = (dateString: string): boolean => {
  const today = formatDate(new Date());
  return dateString === today;
};

export const exportData = (): string => {
  const data = getData();
  return JSON.stringify(data, null, 2);
};

export const importData = (jsonString: string): boolean => {
  try {
    const data = JSON.parse(jsonString);
    // Basic validation
    if (data && Array.isArray(data.todos) && Array.isArray(data.journalEntries)) {
      saveData(data);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error importing data:', error);
    return false;
  }
};
