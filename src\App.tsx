import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import TodoList from './components/TodoList';
import Calendar from './components/Calendar';
import JournalEntry from './components/JournalEntry';
import Layout from './components/Layout';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/todos" element={<TodoList />} />
          <Route path="/calendar" element={<Calendar />} />
          <Route path="/journal/:date" element={<JournalEntry />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
