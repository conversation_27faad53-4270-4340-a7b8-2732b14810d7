// Todo Types
export interface Todo {
  id: string;
  title: string;
  completed: boolean;
  dueDate?: string; // ISO date string
  priority: 'low' | 'medium' | 'high';
  createdAt: string; // ISO date string
  completedAt?: string; // ISO date string
}

// Journal Types
export type MoodValue = 1 | 2 | 3 | 4 | 5;

export interface JournalEntry {
  id: string;
  date: string; // YYYY-MM-DD format
  mood?: MoodValue;
  content: string;
  photos?: string[]; // Array of photo URLs/base64
  location?: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

// App State Types
export interface AppData {
  todos: Todo[];
  journalEntries: JournalEntry[];
  lastBackup?: string; // ISO date string
}

// Utility Types
export interface DashboardStats {
  todaysTodos: number;
  incompleteTodos: number;
  hasJournalEntry: boolean;
  currentMood?: MoodValue;
}

export interface MoodOption {
  value: MoodValue;
  emoji: string;
  label: string;
}
