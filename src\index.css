:root {
  /* Color Palette - Calm and Minimalist */
  --color-primary: #6366f1;
  --color-primary-light: #818cf8;
  --color-primary-dark: #4f46e5;

  --color-secondary: #10b981;
  --color-secondary-light: #34d399;
  --color-secondary-dark: #059669;

  --color-accent: #f59e0b;
  --color-accent-light: #fbbf24;
  --color-accent-dark: #d97706;

  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  font-weight: 400;
  color: #0f172a;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

#root {
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-primary);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-dark);
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Utility classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* Component Styles */
.bg-primary { background-color: var(--color-primary); }
.bg-primary-dark { background-color: var(--color-primary-dark); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-secondary-dark { background-color: var(--color-secondary-dark); }
.bg-white { background-color: white; }

.text-primary { color: var(--color-primary); }
.text-white { color: white; }
.text-neutral-700 { color: var(--color-neutral-700); }
.text-neutral-500 { color: var(--color-neutral-500); }
.text-error { color: var(--color-error); }

.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-primary { border-color: var(--color-primary); }
.border-neutral-200 { border-color: var(--color-neutral-200); }
.border-neutral-300 { border-color: var(--color-neutral-300); }
.border-error { border-color: var(--color-error); }

.rounded-md { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-md); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }

.font-medium { font-weight: 500; }

.w-full { width: 100%; }
.block { display: block; }
.inline-flex { display: inline-flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.space-y-1 > * + * { margin-top: 0.25rem; }

.transition-all { transition-property: all; }
.transition-colors { transition-property: color, background-color, border-color; }
.duration-200 { transition-duration: 200ms; }

.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--color-primary); }
.focus\:ring-primary:focus { box-shadow: 0 0 0 2px var(--color-primary); }
.focus\:ring-error:focus { box-shadow: 0 0 0 2px var(--color-error); }
.focus\:ring-offset-2:focus { box-shadow: 0 0 0 2px white, 0 0 0 4px var(--color-primary); }
.focus\:border-transparent:focus { border-color: transparent; }

.hover\:bg-primary-dark:hover { background-color: var(--color-primary-dark); }
.hover\:bg-secondary-dark:hover { background-color: var(--color-secondary-dark); }
.hover\:bg-primary:hover { background-color: var(--color-primary); }
.hover\:bg-primary\/10:hover { background-color: rgb(99 102 241 / 0.1); }
.hover\:text-white:hover { color: white; }
.hover\:border-neutral-400:hover { border-color: var(--color-neutral-400); }

.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* Additional utility classes */
.min-h-screen { min-height: 100vh; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }

.text-xs { font-size: 0.75rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.bg-neutral-50 { background-color: var(--color-neutral-50); }
.bg-neutral-400 { background-color: var(--color-neutral-400); }
.text-neutral-400 { color: var(--color-neutral-400); }
.text-neutral-600 { color: var(--color-neutral-600); }
.text-neutral-900 { color: var(--color-neutral-900); }

.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }

.bg-primary\/10 { background-color: rgb(99 102 241 / 0.1); }
.bg-secondary\/10 { background-color: rgb(16 185 129 / 0.1); }
.bg-accent\/10 { background-color: rgb(245 158 11 / 0.1); }

.rounded-full { border-radius: 9999px; }

.fixed { position: fixed; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }

.cursor-pointer { cursor: pointer; }

.transition-shadow { transition-property: box-shadow; }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }

.mr-2 { margin-right: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mt-1 { margin-top: 0.25rem; }

.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.hidden { display: none; }

@media (min-width: 768px) {
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Additional utility classes */
.flex-1 { flex: 1 1 0%; }
.min-w-0 { min-width: 0; }
.opacity-75 { opacity: 0.75; }
.line-through { text-decoration: line-through; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.hover\:text-error:hover { color: var(--color-error); }
.hover\:bg-error\/10:hover { background-color: rgb(239 68 68 / 0.1); }
.hover\:border-primary:hover { border-color: var(--color-primary); }
.min-h-0 { min-height: 0; }

/* Calendar specific styles */
.grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
.gap-1 { gap: 0.25rem; }
.min-h-16 { min-height: 4rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.mb-6 { margin-bottom: 1.5rem; }
.border-b { border-bottom-width: 1px; }
.border-neutral-100 { border-color: var(--color-neutral-100); }
.bg-neutral-25 { background-color: #fefefe; }
.bg-neutral-100 { background-color: var(--color-neutral-100); }
.hover\:bg-neutral-50:hover { background-color: var(--color-neutral-50); }
.hover\:bg-neutral-100:hover { background-color: var(--color-neutral-100); }
.relative { position: relative; }
.space-y-1 > * + * { margin-top: 0.25rem; }

/* Journal Entry specific styles */
.h-64 { height: 16rem; }
.resize-none { resize: none; }
.text-right { text-align: right; }
.mt-2 { margin-top: 0.5rem; }
.aspect-square { aspect-ratio: 1 / 1; }
.border-dashed { border-style: dashed; }
.break-all { word-break: break-all; }
.group:hover .group-hover\:opacity-100 { opacity: 1; }
.opacity-0 { opacity: 0; }
.group-hover\:opacity-100 { opacity: 0; }
.-top-2 { top: -0.5rem; }
.-right-2 { right: -0.5rem; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.hover\:scale-105:hover { transform: scale(1.05); }
.flex-wrap { flex-wrap: wrap; }
.gap-3 { gap: 0.75rem; }
.mb-1 { margin-bottom: 0.25rem; }
.hidden { display: none; }
.cursor-pointer { cursor: pointer; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.w-12 { width: 3rem; }
.h-12 { height: 3rem; }
.bottom-4 { bottom: 1rem; }
.right-4 { right: 1rem; }
.bg-accent { background-color: var(--color-accent); }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin { animation: spin 1s linear infinite; }
.border-t-primary { border-top-color: var(--color-primary); }

/* Enhanced transitions and animations */
.transition-transform { transition-property: transform; }
.transition-opacity { transition-property: opacity; }
.duration-150 { transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Mobile-first responsive improvements */
.container {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Touch-friendly button sizes on mobile */
@media (max-width: 767px) {
  button {
    min-height: 44px; /* iOS recommended touch target size */
    min-width: 44px;
  }

  .mobile-padding {
    padding: 1rem;
  }

  /* Larger text on mobile for better readability */
  .mobile-text-lg {
    font-size: 1.125rem;
  }

  /* Better spacing on mobile */
  .mobile-space-y-4 > * + * {
    margin-top: 1rem;
  }

  /* Full width cards on mobile */
  .mobile-full-width {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}

@media (min-width: 768px) {
  .md\:bottom-6 { bottom: 1.5rem; }
  .md\:right-6 { right: 1.5rem; }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Better mobile navigation */
@media (max-width: 767px) {
  .mobile-nav-padding {
    padding-bottom: 5rem; /* Account for mobile navigation */
  }
}

/* Additional responsive utilities */
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.min-h-12 { min-height: 3rem; }

@media (min-width: 640px) {
  .sm\:flex { display: flex; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:items-center { align-items: center; }
  .sm\:justify-between { justify-content: space-between; }
  .sm\:space-y-0 > * + * { margin-top: 0; }
  .sm\:space-x-3 > * + * { margin-left: 0.75rem; }
  .sm\:w-auto { width: auto; }
  .sm\:text-3xl { font-size: 1.875rem; }
  .sm\:text-sm { font-size: 0.875rem; }
  .sm\:text-2xl { font-size: 1.5rem; }
  .sm\:p-3 { padding: 0.75rem; }
  .sm\:p-2 { padding: 0.5rem; }
  .sm\:min-h-16 { min-height: 4rem; }
  .sm\:flex-wrap { flex-wrap: wrap; }
  .sm\:gap-3 { gap: 0.75rem; }
  .sm\:block { display: block; }
}

/* Final polish and accessibility improvements */
.overflow-auto { overflow: auto; }
.bg-neutral-25 { background-color: #fefefe; }

/* Improved focus indicators for better accessibility */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--color-primary), 0 0 0 1px white;
}

/* Better button states */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Smooth page transitions */
.page-transition {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .shadow-sm, .shadow-md, .shadow-lg, .shadow-xl {
    box-shadow: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #0000ff;
    --color-secondary: #008000;
    --color-error: #ff0000;
    --border-light: #000000;
    --border-medium: #000000;
    --border-dark: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-spin,
  .animate-pulse {
    animation: none !important;
  }
}
