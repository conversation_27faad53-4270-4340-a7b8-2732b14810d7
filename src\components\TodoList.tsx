import React, { useState, useEffect } from 'react';
import { Plus, Check, X, ChevronDown, ChevronRight } from 'lucide-react';
import { Card, Button, Input, Select } from './ui';
import { Todo } from '../types';
import { getTodos, saveTodo, deleteTodo, toggleTodo, generateId } from '../utils/storage';
import { formatDisplayDate, formatDateForInput, getTodayString } from '../utils/dateHelpers';
import { PRIORITY_OPTIONS, getPriorityColor } from '../constants/moods';

const TodoList: React.FC = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [showCompleted, setShowCompleted] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newTodo, setNewTodo] = useState({
    title: '',
    dueDate: '',
    priority: 'medium' as const,
  });

  useEffect(() => {
    loadTodos();
  }, []);

  const loadTodos = () => {
    const allTodos = getTodos();
    setTodos(allTodos);
  };

  const handleAddTodo = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newTodo.title.trim()) return;

    const todo: Todo = {
      id: generateId(),
      title: newTodo.title.trim(),
      completed: false,
      dueDate: newTodo.dueDate || undefined,
      priority: newTodo.priority,
      createdAt: new Date().toISOString(),
    };

    saveTodo(todo);
    setNewTodo({ title: '', dueDate: '', priority: 'medium' });
    setShowAddForm(false);
    loadTodos();
  };

  const handleToggleTodo = (todoId: string) => {
    toggleTodo(todoId);
    loadTodos();
  };

  const handleDeleteTodo = (todoId: string) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      deleteTodo(todoId);
      loadTodos();
    }
  };

  const incompleteTodos = todos.filter(todo => !todo.completed);
  const completedTodos = todos.filter(todo => todo.completed);

  const sortTodos = (todoList: Todo[]) => {
    return todoList.sort((a, b) => {
      // Sort by priority first (high -> medium -> low)
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by due date (earliest first)
      if (a.dueDate && b.dueDate) {
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      }
      if (a.dueDate) return -1;
      if (b.dueDate) return 1;

      // Finally by creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">To-Do List</h1>
        <Button
          onClick={() => setShowAddForm(!showAddForm)}
          className="flex items-center justify-center space-x-2 w-full sm:w-auto"
        >
          <Plus className="w-5 h-5" />
          <span>Add Task</span>
        </Button>
      </div>

      {/* Add Todo Form */}
      {showAddForm && (
        <Card>
          <form onSubmit={handleAddTodo} className="space-y-4">
            <h3 className="text-lg font-semibold text-neutral-900">Add New Task</h3>

            <Input
              label="Task Title"
              value={newTodo.title}
              onChange={(e) => setNewTodo({ ...newTodo, title: e.target.value })}
              placeholder="Enter task description..."
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Due Date (Optional)"
                type="date"
                value={newTodo.dueDate}
                onChange={(e) => setNewTodo({ ...newTodo, dueDate: e.target.value })}
                min={getTodayString()}
              />

              <Select
                label="Priority"
                value={newTodo.priority}
                onChange={(e) => setNewTodo({ ...newTodo, priority: e.target.value as any })}
                options={PRIORITY_OPTIONS}
              />
            </div>

            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
              <Button type="submit" className="w-full sm:w-auto">Add Task</Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Incomplete Todos */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-neutral-900">
            Active Tasks ({incompleteTodos.length})
          </h2>
        </div>

        {incompleteTodos.length === 0 ? (
          <Card>
            <div className="text-center py-8">
              <Check className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
              <p className="text-neutral-600">
                No active tasks. Great job! 🎉
              </p>
            </div>
          </Card>
        ) : (
          <div className="space-y-3">
            {sortTodos(incompleteTodos).map((todo) => (
              <TodoItem
                key={todo.id}
                todo={todo}
                onToggle={handleToggleTodo}
                onDelete={handleDeleteTodo}
              />
            ))}
          </div>
        )}
      </div>

      {/* Completed Todos */}
      {completedTodos.length > 0 && (
        <div className="space-y-4">
          <button
            onClick={() => setShowCompleted(!showCompleted)}
            className="flex items-center space-x-2 text-neutral-600 hover:text-neutral-900 transition-colors"
          >
            {showCompleted ? (
              <ChevronDown className="w-5 h-5" />
            ) : (
              <ChevronRight className="w-5 h-5" />
            )}
            <span className="font-medium">
              Completed Tasks ({completedTodos.length})
            </span>
          </button>

          {showCompleted && (
            <div className="space-y-3">
              {sortTodos(completedTodos).map((todo) => (
                <TodoItem
                  key={todo.id}
                  todo={todo}
                  onToggle={handleToggleTodo}
                  onDelete={handleDeleteTodo}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// TodoItem Component
interface TodoItemProps {
  todo: Todo;
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
}

const TodoItem: React.FC<TodoItemProps> = ({ todo, onToggle, onDelete }) => {
  const isOverdue = todo.dueDate && new Date(todo.dueDate) < new Date() && !todo.completed;

  return (
    <Card className={`transition-all ${todo.completed ? 'opacity-75' : ''}`}>
      <div className="flex items-center space-x-4">
        {/* Checkbox */}
        <button
          onClick={() => onToggle(todo.id)}
          className={`w-6 h-6 rounded-md border-2 flex items-center justify-center transition-colors ${
            todo.completed
              ? 'bg-primary border-primary text-white'
              : 'border-neutral-300 hover:border-primary'
          }`}
        >
          {todo.completed && <Check className="w-4 h-4" />}
        </button>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className={`font-medium ${
              todo.completed
                ? 'line-through text-neutral-500'
                : 'text-neutral-900'
            }`}>
              {todo.title}
            </h3>

            {/* Priority Badge */}
            <span className={`text-xs font-medium px-2 py-1 rounded-full ${getPriorityColor(todo.priority)}`}>
              {todo.priority.charAt(0).toUpperCase() + todo.priority.slice(1)}
            </span>
          </div>

          {/* Due Date */}
          {todo.dueDate && (
            <p className={`text-sm mt-1 ${
              isOverdue
                ? 'text-error font-medium'
                : todo.completed
                  ? 'text-neutral-400'
                  : 'text-neutral-600'
            }`}>
              Due: {formatDisplayDate(new Date(todo.dueDate))}
              {isOverdue && ' (Overdue)'}
            </p>
          )}
        </div>

        {/* Delete Button */}
        <button
          onClick={() => onDelete(todo.id)}
          className="w-8 h-8 rounded-md text-neutral-400 hover:text-error hover:bg-error/10 transition-colors flex items-center justify-center"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </Card>
  );
};

export default TodoList;
