import type { Todo, JournalEntry } from '../types';
import { generateId, formatDate } from './storage';

export const generateDemoData = () => {
  const today = new Date();
  const demoTodos: Todo[] = [
    {
      id: generateId(),
      title: 'Review daily journal app features',
      completed: false,
      dueDate: formatDate(today),
      priority: 'high',
      createdAt: new Date().toISOString(),
    },
    {
      id: generateId(),
      title: 'Plan weekend activities',
      completed: false,
      dueDate: formatDate(new Date(today.getTime() + 24 * 60 * 60 * 1000)),
      priority: 'medium',
      createdAt: new Date().toISOString(),
    },
    {
      id: generateId(),
      title: 'Buy groceries',
      completed: true,
      priority: 'low',
      createdAt: new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString(),
      completedAt: new Date().toISOString(),
    },
    {
      id: generateId(),
      title: 'Call family',
      completed: false,
      priority: 'medium',
      createdAt: new Date().toISOString(),
    },
  ];

  const demoJournalEntries: JournalEntry[] = [
    {
      id: generateId(),
      date: formatDate(today),
      mood: 4,
      content: 'Today was a productive day! I worked on the daily journal app and made great progress. The minimalist design is coming together nicely.',
      location: 'Home Office',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: generateId(),
      date: formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000)),
      mood: 3,
      content: 'Had a quiet day. Spent time reading and relaxing. Sometimes it\'s good to take things slow.',
      createdAt: new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: generateId(),
      date: formatDate(new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000)),
      mood: 5,
      content: 'Amazing day! Went hiking with friends and enjoyed beautiful weather. Feeling grateful for good company and nature.',
      location: 'Mountain Trail',
      createdAt: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];

  return { demoTodos, demoJournalEntries };
};

export const loadDemoData = () => {
  const { demoTodos, demoJournalEntries } = generateDemoData();
  
  // Only load demo data if localStorage is empty
  const existingData = localStorage.getItem('daily-journal-app-data');
  if (!existingData) {
    const demoData = {
      todos: demoTodos,
      journalEntries: demoJournalEntries,
      lastBackup: new Date().toISOString(),
    };
    
    localStorage.setItem('daily-journal-app-data', JSON.stringify(demoData));
    console.log('Demo data loaded successfully!');
    return true;
  }
  
  return false;
};
